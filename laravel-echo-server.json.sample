{"authHost": "http://localhost:8080", "authEndpoint": "/broadcasting/auth", "clients": [{"appId": "d1a0b7039cee918e", "key": "17273cf5c8db14d688a991bc3101540d"}], "database": "redis", "databaseConfig": {"redis": {"host": "localhost"}, "sqlite": {"databasePath": "/database/laravel-echo-server.sqlite"}}, "devMode": true, "host": null, "port": "8080", "protocol": "http", "socketio": {}, "secureOptions": 67108864, "sslCertPath": "", "sslKeyPath": "", "sslCertChainPath": "", "sslPassphrase": "", "subscribers": {"http": true, "redis": true}, "apiOriginAllow": {"allowCors": true, "allowOrigin": "http://localhost:8080", "allowMethods": "GET, POST", "allowHeaders": "Origin, Content-Type, X-Auth-Token, X-Requested-With, Accept, Authorization, X-CSRF-TOKEN, X-Socket-Id"}}