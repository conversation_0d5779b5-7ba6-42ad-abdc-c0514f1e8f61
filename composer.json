{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "7.4.*", "ext-fileinfo": "*", "ext-json": "*", "facade/ignition": "^2.3.6", "fideloper/proxy": "*", "google/apiclient": "*", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "*", "laravel-frontend-presets/material-dashboard": "^1.1.2", "laravel/framework": "^8.0", "laravel/socialite": "*", "laravel/tinker": "*", "laravel/ui": "^3.0", "morrislaptop/laravel-queue-clear": "*", "predis/predis": "*", "stechstudio/laravel-zipstream": "^4.13"}, "require-dev": {"barryvdh/laravel-ide-helper": "*", "beyondcode/laravel-dump-server": "*", "doctrine/dbal": "*", "filp/whoops": "*", "fzaninotto/faker": "*", "mockery/mockery": "*", "nunomaduro/collision": "^5.0", "oscarafdev/migrations-generator": "^2.0", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "*"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}