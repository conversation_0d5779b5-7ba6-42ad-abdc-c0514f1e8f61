{"private": true, "scripts": {"start": "php artisan queue:work >> storage/logs/laravel-echo.log 2>&1 & node node_modules/laravel-echo-server/bin/server.js start >> storage/logs/laravel-echo.log 2>&1 &", "start-std": "php artisan queue:work & node node_modules/laravel-echo-server/bin/server.js start &", "start-nolog": "php artisan queue:work > /dev/null 2>&1 & node node_modules/laravel-echo-server/bin/server.js start > /dev/null 2>&1 &", "kill-queue": " kill -KILL `ps ax | grep queue:work |grep -v \"grep\" | awk '{print $1}'`", "dev": "npm run development", "development": "cross-env NODE_OPTIONS='--openssl-legacy-provider' NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_OPTIONS='--openssl-legacy-provider' NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_OPTIONS='--openssl-legacy-provider' NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.18", "bootstrap": "4.2.1", "cross-env": "^5.1", "jquery": "^3.2", "laravel-mix": "^4.0.7", "lodash": "^4.17.5", "popper.js": "^1.12", "resolve-url-loader": "^2.3.1", "sass": "^1.2.14", "sass-loader": "^7.1.0", "vue": "^2.5.17", "vue-template-compiler": "^2.6.10"}, "dependencies": {"bootcards": "^1.1.2", "install": "^0.13.0", "izitoast": "^1.4.0", "laravel-echo": "^1.6.1", "laravel-echo-server": "^1.6.2", "lity": "^2.4.1", "material-dashboard": "^2.1.0", "muuri": "^0.9.1", "npm-cli": "^0.1.0", "npmlog": "4.1.2", "slick-carousel": "^1.8.1", "slick-slider": "^1.8.2", "socket.io-client": "^2.3.0", "unsupported": "^1.1.0", "vue-material": "^1.0.0-beta-11", "web-animations-js": "^2.3.2"}}