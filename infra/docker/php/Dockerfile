FROM php:7-fpm-buster
LABEL maintainer="ucan-lab <<EMAIL>>"
SHELL ["/bin/bash", "-oeux", "pipefail", "-c"]

# timezone environment
ENV TZ=JST \
  # locale
  LANG=ja_JP.UTF-8 \
  LANGUAGE=ja_JP:ja \
  LC_ALL=ja_JP.UTF-8 \
  # composer environment
  COMPOSER_ALLOW_SUPERUSER=1 \
  COMPOSER_HOME=/composer

COPY --from=composer:2.0 /usr/bin/composer /usr/bin/composer

RUN sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list
RUN sed -i 's/security.debian.org/archive.debian.org/g' /etc/apt/sources.list

RUN apt-get update && \
  apt-get -y install task-japanese git libicu-dev libonig-dev libzip-dev gzip unzip locales imagemagick libmagickwand-dev && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/* && \
  locale-gen en_US.UTF-8 && \
  locale-gen ja_JP.UTF-8 && \
  localedef -f UTF-8 -i en_US en_US.UTF-8 && \
  localedef -f UTF-8 -i ja_JP ja_JP && \
  mkdir /var/run/php-fpm
RUN docker-php-ext-install intl pdo_mysql zip bcmath
RUN pecl install imagick
RUN docker-php-ext-enable imagick
RUN composer config -g process-timeout 3600 && \
    composer config -g repos.packagist composer https://packagist.org

COPY ./infra/docker/php/php-fpm.d/zzz-www.conf /usr/local/etc/php-fpm.d/zzz-www.conf
COPY ./infra/docker/php/php.ini /usr/local/etc/php/php.ini

WORKDIR /work
