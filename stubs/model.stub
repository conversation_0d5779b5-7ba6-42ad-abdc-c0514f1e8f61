<?php

namespace {{ namespace }};

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class {{ class }} extends BaseModel
{
    // todo: if have factories, this use.
    //use HasFactory;

    use SoftDeletes;

    protected $table = ''; // table name.
    protected $primaryKey = ''; // primary key name.

    protected $guarded = [
        'created_at',
        'updated_at',
    ];

    // setting casts.
    protected $casts = [

    ];

    // setting hidden data columns. (option)
    protected $hidden = [

    ];
}
