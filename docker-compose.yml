volumes:
  php-fpm-socket:
  db-store:
  redis-volume:
services:
  app:
    build:
      context: .
      dockerfile: ./infra/docker/php/Dockerfile
      args:
        - TZ=${APP_TZ:-Asia/Tokyo}
    volumes:
      - php-fpm-socket:/var/run/php-fpm
      - ./:/work
    environment:
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=${DB_PORT:-3306} # appコンテナから見たDBコンテナのポートなので、HOST_DB_PORTにしてはいけない
      - DB_DATABASE=${DB_DATABASE:-laravel_local}
      - DB_USERNAME=${DB_USERNAME:-phper}
      - DB_PASSWORD=${DB_PASSWORD:-secret}

  web:
    build:
      context: .
      dockerfile: ./infra/docker/nginx/Dockerfile
    ports:
      - ${WEB_PORT:-80}:80
    volumes:
      - php-fpm-socket:/var/run/php-fpm
      - ./:/work

  db:
    build:
      context: .
      dockerfile: ./infra/docker/mysql/Dockerfile
    ports:
      - ${HOST_DB_PORT:-3306}:3306
    volumes:
      - db-store:/var/lib/mysql:cached
      - ./infra/dbBackup:/dbBackup
    environment:
      - MYSQL_DATABASE=${DB_DATABASE:-laravel_local}
      - MYSQL_USER=${DB_USERNAME:-phper}
      - MYSQL_PASSWORD=${DB_PASSWORD:-secret}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-secret}
    command: --default_authentication_plugin=mysql_native_password

  redis:
    image: redis:latest
    container_name: redis
    command: redis-server --appendonly yes
    volumes:
      - redis-volume:/data
    ports:
      - ${HOST_REDIS_PORT:-6379}:6379

  mailhog:
    image: mailhog/mailhog
    ports:
      - ${HOST_MAILHOG_WEB_PORT:-8025}:8025
      - ${HOST_MAIL_PORT:-1025}:1025
