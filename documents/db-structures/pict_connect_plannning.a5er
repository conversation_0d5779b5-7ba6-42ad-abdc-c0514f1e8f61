# A5:ER FORMAT:17
# A5:ER ENCODING:UTF8
# A5:ER Mk-1 Copyright © 2007 m.matsubara
# A5:SQL Mk-2 Version 2.18.4 Copyright © 1997 - 2023 m.matsubara
#  https://a5m2.mmatsubara.com

[Manager]
ProjectName=pict-connect
Author=<PERSON><PERSON><PERSON> Oolong
MaxEntityRowShow=1000
ReadOnlyRecommend=0
Page=Main
PageInfo="Main",2,"A2Landscape",$FFFFFF
LogicalView=1
DecodeDomain=0
ViewModePageIndividually=1
ViewMode=2
ViewFormat=0
UseNondependenceDashLine=0
FontName=Tahoma
FontSize=6
PaperSize=A4Landscape
HeaderLeft=
HeaderCenter=
HeaderRight=
FooterLeft=
FooterCenter=
FooterRight=
ShowPageoutRelation=1
RDBMSType=6
RDBMSTypeName=MySQL
CommonField="作成日時","created_at","datetime",,,"","",$FFFFFFFF
CommonField="更新日時","updated_at","datetime",,,"","",$FFFFFFFF
CommonField="削除日時","deleted_at","datetime",,,"","",$FFFFFFFF
DefaultPkName=%0:s_PKC
DefaultPkIndexName=%0:s_PKI
DefaultIndexName=%0:s_IX%1:d
DefaultFkName=%0:s_FK%1:d
SqlSeparator=0
UpperCaseKeyword=0
ShowTag=1
ShowCommonAttributes=1
BugFixEntityWidth=1

[Entity]
PName=migrations
LName=migrations
Comment=
TableOption=
Page=MAIN
Left=50
Top=50
Field="id","id","int unsigned auto_increment","NOT NULL",0,"","",$FFFFFFFF,""
Field="migration","migration","varchar(255)","NOT NULL",,"","",$FFFFFFFF,""
Field="batch","batch","int","NOT NULL",,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
Position="MAIN",50,50
ZOrder=-1

[Entity]
PName=password_resets
LName=password_resets
Comment=
TableOption=
Page=MAIN
Left=50
Top=250
Field="メールアドレス","email","varchar(255)","NOT NULL",,"","",$FFFFFFFF,""
Field="パスワードリセットトークン","token","varchar(255)","NOT NULL",,"","",$FFFFFFFF,""
Index=password_resets_email_index=0,email
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805133359
Position="MAIN",50,250
ZOrder=-2

[Entity]
PName=photos
LName=写真
Comment=
TableOption=
Page=MAIN
Left=450
Top=50
Field="写真id","photo_id","bigint unsigned auto_increment","NOT NULL",0,"","",$FFFFFFFF,""
Field="投稿ユーザーid","user_id","bigint","NOT NULL",,"","",$FFFFFFFF,""
Field="写真保存パス","store_path","text",,,"","",$FFFFFFFF,""
Index=photo_id=2,photo_id
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805133444
Position="MAIN",450,50
ZOrder=-3

[Entity]
PName=sns_id_lists
LName=SNSidリスト
Comment=
TableOption=
Page=MAIN
Left=750
Top=150
Field="pict_connectユーザーid","pc_user_id","bigint unsigned","NOT NULL",0,"","",$FFFFFFFF,""
Field="連携先SNSid","sns_id","bigint","NOT NULL",,"","Twtter:ユーザーが自由に指定できるscreen_nameではなく、一意に降られているidを登録する点に注意\n他のSNSでも同様のパラメーターがあればそれを使用する。なければユーザーidを使用する\n必要に応じてbigint => varchar に変更",$FFFFFFFF,""
Field="SNS種別","sns_type","tinyint","NOT NULL",,"","0: pict_connect\n1: Twitter\n2: Mastodon\n3: Misskey",$FFFFFFFF,""
Index=sns_id_lists_pc_user_id_uindex=2,pc_user_id
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230916185037
Position="MAIN",750,150
ZOrder=-4

[Entity]
PName=users
LName=ユーザー
Comment=
TableOption=
Page=MAIN
Left=1250
Top=50
Field="ユーザーid","user_id","bigint unsigned","NOT NULL",0,"","",$FFFFFFFF,""
Field="ScreenName","screen_name","varchar(255)",,,"","",$FFFFFFFF,""
Field="表示名","view_name","varchar(255)",,,"","",$FFFFFFFF,""
Field="パスワード(ハッシュ化済み)","password","varchar(255)",,,"","SNSログインの場合は任意",$FFFFFFFF,""
Field="ユーザーアイコンのパス","user_icon_path","text",,,"","任意",$FFFFFFFF,""
Field="認証トークン","token","text",,,"","自動生成",$FFFFFFFF,""
Field="認証トークン(sec)","token_sec","text",,,"","自動生成",$FFFFFFFF,""
Field="rememberトークン","remember_token","varchar(100)",,,"","Laravel標準 自動生成",$FFFFFFFF,""
Field="備考","description","text",,,"","",$FFFFFFFF,""
Field="SNSログインを利用して登録したユーザーか？","is_from_sns","tinyint","NOT NULL",,"0","",$FFFFFFFF,""
Field="メールアドレス","email","varchar(255)",,,"","",$FFFFFFFF,""
Index=users_user_id_uindex=2,user_id
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805133329
Position="MAIN",1250,50
ZOrder=-5

[Entity]
PName=album_masters
LName=アルバムマスター
Comment=
TableOption=
Page=MAIN
Left=400
Top=350
Field="アルバムマスタid","album_master_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="作成ユーザーid","user_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="イベントid","event_id","BIGINT UNSIGNED",,,"","指定されない場合は個人のアルバムとして扱う",$FFFFFFFF,""
Field="公開範囲フラグ","open_range_flag","TINYINT(1) UNSIGNED","NOT NULL",,"","0: パブリック\n1: 限定公開(URL共有)\n2: プライベート(ログインユーザーのみ閲覧可能)",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805125036
Position="MAIN",400,350
ZOrder=1

[Relation]
Entity1=users
Entity2=sns_id_lists
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=pc_user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,345,5374,942,R,R,""
Dependence=1
Caption=
PName=
LineMode=0
Bar1=34
Bar2=537
Bar3=94
TermPos1=R
TermPos2=R
ZOrder=2

[Relation]
Entity1=users
Entity2=album_masters
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,345,8374,2175,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=34
Bar2=837
Bar3=218
TermPos1=R
TermPos2=R
ZOrder=3

[Relation]
Entity1=users
Entity2=photos
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,345,8239,2683,R,R,""
Dependence=0
Caption=
PName=
ModifiedDateTime=20230805135744
LineMode=0
Bar1=34
Bar2=824
Bar3=268
TermPos1=R
TermPos2=R
ZOrder=4

[Entity]
PName=album_photos
LName=アルバム-写真
Comment=
TableOption=
Page=MAIN
Left=750
Top=500
Field="アルバム-写真id","album_photo_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="アルバムマスタid","album_master_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="イベントid","event_id","BIGINT UNSIGNED",,,"","マスターの情報と同期（マスタ経由で辿ってもいいが、イベントグリッドショーを出す時にリレーションやフィルタがめんどくさそうなので追加）\nない場合は個人のアルバムという意味",$FFFFFFFF,""
Field="写真id","photo_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230913233044
Position="MAIN",750,500
ZOrder=5

[Relation]
Entity1=photos
Entity2=album_photos
RelationType1=2
RelationType2=3
Fields1=photo_id
Fields2=photo_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,9617,6895,1683,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=962
Bar2=690
Bar3=168
TermPos1=R
TermPos2=R
ZOrder=7

[Relation]
Entity1=album_masters
Entity2=album_photos
RelationType1=2
RelationType2=3
Fields1=album_master_id
Fields2=album_master_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,833,7537,2201,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=83
Bar2=754
Bar3=220
TermPos1=R
TermPos2=R
ZOrder=9

[Entity]
PName=events
LName=イベント
Comment=
TableOption=
Page=MAIN
Left=250
Top=650
Field="イベントid","event_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="イベント名","event_name","varchar(255)","NOT NULL",,"","",$FFFFFFFF,""
Field="イベント管理者id","event_admin_id","BIGINT UNSIGNED","NOT NULL",,"","初期値は作成ユーザーのid",$FFFFFFFF,""
Field="イベント詳細","event_detail","text",,,"","",$FFFFFFFF,""
Field="備考","description","text",,,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805135126
Position="MAIN",250,650
ZOrder=10

[Relation]
Entity1=users
Entity2=events
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=event_admin_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,345,8656,3245,R,R,""
Dependence=0
Caption=
PName=
ModifiedDateTime=20230805135310
LineMode=0
Bar1=34
Bar2=866
Bar3=324
TermPos1=R
TermPos2=R
ZOrder=11

[Entity]
PName=event_participants
LName=イベント参加者
Comment=
TableOption=
Page=MAIN
Left=600
Top=800
Field="イベント参加者id","event_participants_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="イベントid","event_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="参加者ユーザーid","user_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805135546
Position="MAIN",600,800
ZOrder=12

[Relation]
Entity1=events
Entity2=event_participants
RelationType1=2
RelationType2=3
Fields1=event_id
Fields2=event_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,794,6335,2572,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=79
Bar2=634
Bar3=257
TermPos1=R
TermPos2=R
ZOrder=13

[Relation]
Entity1=users
Entity2=event_participants
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,345,7520,4451,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=34
Bar2=752
Bar3=445
TermPos1=R
TermPos2=R
ZOrder=14

[Entity]
PName=photo_comments
LName=写真コメント
Comment=
TableOption=
Page=MAIN
Left=1250
Top=550
Field="写真コメントid","photo_comment_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="アルバム-写真id","album_photo_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="投稿者id","author_user_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="コメント内容","comment","TEXT","NOT NULL",,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805140255
Position="MAIN",1250,550
ZOrder=15

[Relation]
Entity1=users
Entity2=photo_comments
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=author_user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,4040,5960,4434,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=404
Bar2=596
Bar3=443
TermPos1=R
TermPos2=R
ZOrder=16

[Relation]
Entity1=album_photos
Entity2=photo_comments
RelationType1=2
RelationType2=3
Fields1=album_photo_id
Fields2=album_photo_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,940,7328,2120,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=94
Bar2=733
Bar3=212
TermPos1=R
TermPos2=R
ZOrder=18

[Relation]
Entity1=events
Entity2=album_photos
RelationType1=1
RelationType2=3
Fields1=event_id
Fields2=event_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,794,7537,3741,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=79
Bar2=754
Bar3=374
TermPos1=R
TermPos2=R
ZOrder=19

[Entity]
PName=photo_reactions
LName=写真リアクション
Comment=
TableOption=
Page=MAIN
Left=1250
Top=800
Field="写真コメントid","photo_reaction_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="アルバム-写真id","album_photo_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="投稿者id","reaction_user_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="リアクション内容","reaction","varchar(255)","NOT NULL",,":heart:","misskey,discord\nのような \n:hoge:\nのような形式。\n初期は:heart:のみ。",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230805140809
Position="MAIN",1250,800
ZOrder=20

[Relation]
Entity1=users
Entity2=photo_reactions
RelationType1=2
RelationType2=3
Fields1=user_id
Fields2=reaction_user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,7824,9402,4434,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=782
Bar2=940
Bar3=443
TermPos1=R
TermPos2=R
ZOrder=21

[Relation]
Entity1=album_photos
Entity2=photo_reactions
RelationType1=2
RelationType2=3
Fields1=album_photo_id
Fields2=album_photo_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,3718,7339,2205,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=372
Bar2=734
Bar3=220
TermPos1=R
TermPos2=R
ZOrder=22

[Entity]
PName=album_access_authorities
LName=アルバムアクセス権限設定
Comment=
TableOption=
Page=MAIN
Left=950
Top=1100
Field="アルバムアクセス権限id","album_access_authority_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT UNIQUE"
Field="アルバム-写真id","album_photo_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="アクセストークン","token","varchar(255)",,,"","authorized_user_id と sns_screen_name のいずれも入っていなければ必須",$FFFFFFFF,""
Field="連携先SNS スクリーンネーム(@hoge)","sns_screen_name","varchar(255)",,,"","token と authorized_user_id いずれも入っていなければ必須\nTwitterのscreen_name(@hoge)にあたるものを入れる",$FFFFFFFF,""
Field="承認済みユーザー","authorized_user_id","BIGINT UNSIGNED",,,"","token と sns_screen_name いずれも入っていなければ必須",$FFFFFFFF,""
Field="アルバム内容の変更の可否","is_writable","TINYINT(1)","NOT NULL",,"false","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230916191752
Position="MAIN",950,1100
ZOrder=23

[Relation]
Entity1=album_photos
Entity2=album_access_authorities
RelationType1=2
RelationType2=3
Fields1=album_photo_id
Fields2=album_photo_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,8389,7339,2601,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=839
Bar2=734
Bar3=260
TermPos1=R
TermPos2=R
ZOrder=24

[Relation]
Entity1=users
Entity2=album_access_authorities
RelationType1=1
RelationType2=3
Fields1=user_id
Fields2=authorized_user_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,7854,9326,9905,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=785
Bar2=933
Bar3=990
TermPos1=R
TermPos2=R
ZOrder=25

[Entity]
PName=event_join_tokens
LName=イベント参加トークン
Comment=
TableOption=
Page=MAIN
Left=200
Top=1000
Field="イベント参加トークンid","event_join_token_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"UNIQUE AUTO_INCREMENT"
Field="イベントid","event_id","BIGINT UNSIGNED","NOT NULL",,"","",$FFFFFFFF,""
Field="有効期限","expired_at","DATETIME",,,"","nullの場合は無制限",$FFFFFFFF,""
Field="有効使用回数","limit_times","int unisgned",,,"","nullの場合は無制限",$FFFFFFFF,""
Field="使用された回数","use_times","int unisgned",,,"","limit_timesが指定されていない場合はnull",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230907121500
Position="MAIN",200,1000,220,203
ZOrder=26

[Relation]
Entity1=events
Entity2=event_join_tokens
RelationType1=2
RelationType2=3
Fields1=event_id
Fields2=event_id
Cardinarity1=
Cardinarity2=
Position="MAIN",0,466,9444,646,R,R,""
Dependence=0
Caption=
PName=
LineMode=0
Bar1=47
Bar2=944
Bar3=65
TermPos1=R
TermPos2=R
ZOrder=27

[Entity]
PName=guest_logins
LName=ゲストログイン
Comment=SNSのID(screen name)を使用してアルバムにアクセスする権限があるかを評価するためのテーブル
TableOption=
Page=Main
Left=1500
Top=1150
Field="ゲストログインid","guest_login_id","BIGINT UNSIGNED","NOT NULL",0,"","",$FFFFFFFF,"AUTO_INCREMENT"
Field="連携先SNS スクリーンネーム(@hoge)","sns_screen_name","varchar(255)","NOT NULL",,"","Twitterのscreen_name(@hoge)にあたるものを入れる",$FFFFFFFF,""
Field="SNS種別","sns_type","TINYINT(1)","NOT NULL",,"","",$FFFFFFFF,""
Field="認証トークン","guest_token","varchar(64)","NOT NULL",,"","",$FFFFFFFF,""
EffectMode=None
Color=$000000
BkColor=$FFFFFF
ModifiedDateTime=20230916190315
Position="Main",1500,1150
ZOrder=28

[Relation]
Entity1=guest_logins
Entity2=album_access_authorities
RelationType1=4
RelationType2=3
Fields1=sns_screen_name
Fields2=sns_screen_name
Cardinarity1=
Cardinarity2=
Position="MAIN",0,4183,5817,4267,R,R,""
Dependence=0
Caption=
PName=
ModifiedDateTime=20230916191219
LineMode=0
Bar1=418
Bar2=582
Bar3=427
TermPos1=R
TermPos2=R
ZOrder=29
