# イベント (events)

## テーブル情報

| 項目                           | 値                                                                                                   |
|:-------------------------------|:-----------------------------------------------------------------------------------------------------|
| システム名                     | pict-connect                                                                                         |
| サブシステム名                 |                                                                                                      |
| 論理エンティティ名             | イベント                                                                                             |
| 物理エンティティ名             | events                                                                                               |
| 作成者                         | Tatsuya Ootani - Oolong                                                                              |
| 作成日                         | 2023/08/05                                                                                           |
| タグ                           |                                                                                                      |



## カラム情報

| No. | 論理名                         | 物理名                         | データ型                       | Not Null | デフォルト           | 備考                           |
|----:|:-------------------------------|:-------------------------------|:-------------------------------|:---------|:---------------------|:-------------------------------|
|   1 | イベントid                     | event_id                       | BIGINT UNSIGNED                | Yes (PK) |                      |                                |
|   2 | イベント名                     | event_name                     | varchar(255)                   | Yes      |                      |                                |
|   3 | イベント管理者id               | event_admin_id                 | BIGINT UNSIGNED                | Yes      |                      | 初期値は作成ユーザーのid       |
|   4 | イベント詳細                   | event_detail                   | text                           |          |                      |                                |
|   5 | 備考                           | description                    | text                           |          |                      |                                |
|   6 | 作成日時                       | created_at                     | datetime                       |          |                      |                                |
|   7 | 更新日時                       | updated_at                     | datetime                       |          |                      |                                |
|   8 | 削除日時                       | deleted_at                     | datetime                       |          |                      |                                |



## インデックス情報

| No. | インデックス名                 | カラムリスト                             | ユニーク   | オプション                     | 
|----:|:-------------------------------|:-----------------------------------------|:-----------|:-------------------------------|



## リレーションシップ情報

| No. | 動詞句                         | カラムリスト                             | 参照先                         | 参照先カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|
|   1 |                                | event_admin_id                           | users                          | user_id                                  |



## リレーションシップ情報(PK側)

| No. | 動詞句                         | カラムリスト                             | 参照元                         | 参照元カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|
|   1 |                                | event_id                                 | album_photos                   | event_id                                 |
|   2 |                                | event_id                                 | event_participants             | event_id                                 |


