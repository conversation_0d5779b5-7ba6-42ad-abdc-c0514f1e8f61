# アルバムマスター (album_masters)

## テーブル情報

| 項目                           | 値                                                                                                   |
|:-------------------------------|:-----------------------------------------------------------------------------------------------------|
| システム名                     | pict-connect                                                                                         |
| サブシステム名                 |                                                                                                      |
| 論理エンティティ名             | アルバムマスター                                                                                     |
| 物理エンティティ名             | album_masters                                                                                        |
| 作成者                         | <PERSON><PERSON><PERSON> - <PERSON>                                                                              |
| 作成日                         | 2023/08/05                                                                                           |
| タグ                           |                                                                                                      |



## カラム情報

| No. | 論理名                         | 物理名                         | データ型                       | Not Null | デフォルト           | 備考                           |
|----:|:-------------------------------|:-------------------------------|:-------------------------------|:---------|:---------------------|:-------------------------------|
|   1 | アルバムマスタid               | album_master_id                | BIGINT UNSIGNED                | Yes (PK) |                      |                                |
|   2 | 作成ユーザーid                 | user_id                        | BIGINT UNSIGNED                | Yes      |                      |                                |
|   3 | イベントid                     | event_id                       | BIGINT UNSIGNED                |          |                      | 指定されない場合は個人のアルバムとして扱う |
|   4 | 公開範囲フラグ                 | open_range_flag                | TINYINT(1) UNSIGNED            | Yes      |                      | 0: パブリック<br>1: 限定公開(URL共有)<br>2: プライベート(ログインユーザーのみ閲覧可能) |
|   5 | 作成日時                       | created_at                     | datetime                       |          |                      |                                |
|   6 | 更新日時                       | updated_at                     | datetime                       |          |                      |                                |
|   7 | 削除日時                       | deleted_at                     | datetime                       |          |                      |                                |



## インデックス情報

| No. | インデックス名                 | カラムリスト                             | ユニーク   | オプション                     | 
|----:|:-------------------------------|:-----------------------------------------|:-----------|:-------------------------------|



## リレーションシップ情報

| No. | 動詞句                         | カラムリスト                             | 参照先                         | 参照先カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|
|   1 |                                | user_id                                  | users                          | user_id                                  |



## リレーションシップ情報(PK側)

| No. | 動詞句                         | カラムリスト                             | 参照元                         | 参照元カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|
|   1 |                                | album_master_id                          | album_photos                   | album_master_id                          |


