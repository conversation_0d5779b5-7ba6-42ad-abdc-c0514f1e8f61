# ユーザー (users)

## テーブル情報

| 項目                           | 値                                                                                                   |
|:-------------------------------|:-----------------------------------------------------------------------------------------------------|
| システム名                     | pict-connect                                                                                         |
| サブシステム名                 |                                                                                                      |
| 論理エンティティ名             | ユーザー                                                                                             |
| 物理エンティティ名             | users                                                                                                |
| 作成者                         | Tatsuya Ootani - Oolong                                                                              |
| 作成日                         | 2023/08/05                                                                                           |
| タグ                           |                                                                                                      |



## カラム情報

| No. | 論理名                         | 物理名                         | データ型                       | Not Null | デフォルト           | 備考                           |
|----:|:-------------------------------|:-------------------------------|:-------------------------------|:---------|:---------------------|:-------------------------------|
|   1 | ユーザーid                     | user_id                        | bigint unsigned                | Yes (PK) |                      |                                |
|   2 | ScreenName                     | screen_name                    | varchar(255)                   |          |                      |                                |
|   3 | 表示名                         | view_name                      | varchar(255)                   |          |                      |                                |
|   4 | パスワード(ハッシュ化済み)     | password                       | varchar(255)                   |          |                      | SNSログインの場合は任意        |
|   5 | ユーザーアイコンのパス         | user_icon_path                 | text                           |          |                      | 任意                           |
|   6 | 認証トークン                   | token                          | text                           |          |                      | 自動生成                       |
|   7 | 認証トークン(sec)              | token_sec                      | text                           |          |                      | 自動生成                       |
|   8 | rememberトークン               | remember_token                 | varchar(100)                   |          |                      | Laravel標準 自動生成           |
|   9 | 備考                           | description                    | text                           |          |                      |                                |
|  10 | SNSログインを利用して登録したユーザーか？ | is_from_sns                    | tinyint                        | Yes      | 0                    |                                |
|  11 | メールアドレス                 | email                          | varchar(255)                   |          |                      |                                |
|  12 | 作成日時                       | created_at                     | datetime                       |          |                      |                                |
|  13 | 更新日時                       | updated_at                     | datetime                       |          |                      |                                |
|  14 | 削除日時                       | deleted_at                     | datetime                       |          |                      |                                |



## インデックス情報

| No. | インデックス名                 | カラムリスト                             | ユニーク   | オプション                     | 
|----:|:-------------------------------|:-----------------------------------------|:-----------|:-------------------------------|
|   1 | users_user_id_uindex           | user_id                                  | 制約       |                                |



## リレーションシップ情報

| No. | 動詞句                         | カラムリスト                             | 参照先                         | 参照先カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|



## リレーションシップ情報(PK側)

| No. | 動詞句                         | カラムリスト                             | 参照元                         | 参照元カラムリスト                       |
|----:|:-------------------------------|:-----------------------------------------|:-------------------------------|:-----------------------------------------|
|   1 |                                | user_id                                  | album_access_authorities       | authorized_user_id                       |
|   2 |                                | user_id                                  | photo_reactions                | reaction_user_id                         |
|   3 |                                | user_id                                  | photo_comments                 | author_user_id                           |
|   4 |                                | user_id                                  | event_participants             | user_id                                  |
|   5 |                                | user_id                                  | events                         | event_admin_id                           |
|   6 |                                | user_id                                  | photos                         | user_id                                  |
|   7 |                                | user_id                                  | album_masters                  | user_id                                  |
|   8 |                                | user_id                                  | sns_id_lists                   | pc_user_id                               |


