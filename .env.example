APP_NAME=pict-connect
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
WEBSOCKET_URL='http://localhost:8080'

ACCESS_CODE=

WEB_PORT=80 # (Dockerのみ)ホストOSにバインドするWebのポート番号

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306 # 本番 & LaravelからDBコンテナに通信する際のDBのポート番号
HOST_DB_PORT=3306 # (Dockerのみ)ホストOSにバインドするDBのポート番号
DB_DATABASE=pict_connect
DB_USERNAME=pict_connect
DB_PASSWORD=laravel

BROADCAST_DRIVER=redis
CACHE_DRIVER=file
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379 # 本番 & <PERSON><PERSON>からDBコンテナに通信する際のRedisのポート番号
HOST_REDIS_PORT=6379 # (Dockerのみ)ホストOSにバインドするRedisのポート番号

MAIL_DRIVER=smtp
MAIL_HOST=mailhog
MAIL_PORT=25 #本番 & Laravelからメールサーバーコンテナに通信する際のポート番号
HOST_MAILHOG_WEB_PORT=8025 # (Dockerのみ)ホストOSにバインドするモックメールサーバーのWebUIのポート番号
HOST_MAIL_PORT=1025 # (Dockerのみ)ホストOSにバインドするモックメールサーバーのポート番号
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TWITTER_CLIENT_ID= #TwitterのApi Key
TWITTER_CLIENT_SECRET= #TwitterのAPI Secret Key
CALLBACK_URL= #コールバックURL

# 写真キャッシュ時間設定(0で無効化)
PHOTO_CACHE_THUMBNAIL_MAX_AGE=259200
PHOTO_CACHE_FULL_PREVIEW_MAX_AGE=259200
