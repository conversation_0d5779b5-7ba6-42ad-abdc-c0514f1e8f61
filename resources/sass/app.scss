
// Fonts
@import url('https://fonts.googleapis.com/css?family=Nunito');

// Variables
@import 'variables';

// Bootstrap
 @import '~bootstrap/scss/bootstrap';
// @import '~bootstrap-material-design/scss/bootstrap-material-design';

@import "~lity/dist/lity.min.css";

// global settings
$header-height: 45px;

body {
    touch-action: manipulation;
    //height: 100vh !important;
}

//Safari拡大対策
input.form-control {
    font-size: 16px;
}

#main-container {
    height: calc(100vh - $header-height);
    width: 100vw;
    position: relative;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-top: 2%;
    padding-bottom: 70px;
}

.navbar-laravel {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

// gnavつきのナビゲーションバーは2段表示可能
.gnav {
    flex-direction: column;
}

#func-menu-nav{
    .container {
        margin-left: 5px;
        margin-right: 5px;
        min-height: 54px; // ログイン画面のタブがつぶれないように
    }

    overflow-x: hidden;
    width: 100%;
}

.menu-col {
    white-space: nowrap !important;
}

#func-menu-nav {
    padding-left: 0;
    padding-right: 0;
}

.nav-tabs {
    flex-wrap: unset !important;
}
.nav-tabs .nav-item {
    width: calc(100vw / 2) !important;
    text-align: center !important;
}

.container {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
}

// ドロップダウンメニュー
#menu-logout {
    color: red;
    cursor: pointer;
}

#back-icon {
    cursor: pointer;
    background-color: unset;
    border-color: unset;
    border: none;
    position: absolute;
    top: 0;
    span {
        font-weight: bold;
        font-family: "Nunito", sans-serif;
        font-size: 20px;
    }
}

#guest-dropdown-icon {
    font-family: "Nunito", sans-serif;
    font-size: 20px;
}

#user-name {
    text-align: center;
    position: relative;
    width: 100%;
}

#photo-add-button {
    width: 3rem;
    height: 3rem;
    right: 15px;
    bottom: 15px;
    position: fixed;
    box-shadow: #d0d0d087  3px 6px 2px;
}
#photo-add-button img {
    width: 30px;
}

#camera-button {
    width: 3rem;
    height: 3rem;
    right: 80px;
    bottom: 15px;
    position: fixed;
    box-shadow: #d0d0d087  3px 6px 2px;
}

#camera-button img {
    width: 30px;
}

#photo-cards {
    display: block !important;
}

#photo-cards .card-body {
    position: absolute;
    padding: 0px;
    height: 80px;
    bottom: 0;
    width: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 1));
    border-radius: 3px;
}

#photo-cards .card-body img {
    height: 25px !important;
    margin: 40px 5px 5px 10px;
}

#photo-cards .card-body .card-text {
    color: #FFFFFF;
    position: absolute;
    bottom: 15px;
    left: 45px;
    width: calc(100% - 55px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.event-text {
    color: #FFFFFF;
    position: absolute;
    bottom: 15px;
    left: 5px;
    width: calc(100% - 55px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#photo-cards .user-icon {
    width: 25px;
    margin: 5px;
}


@media screen and (max-width: 699px) {
    #photo-cards {
        //padding-bottom: 80px !important;
    }
}

@media screen and (min-width: 700px) {
    #photo-cards {
        width: 98%;
        display: block;
        margin: 0 auto;
        //padding: 0;
    }
}

.photo-input-hidden {
    width: 0;
    height: 0;
    display: none;
    cursor: pointer;
}

button {
    width: auto;
    padding:0;
    margin:0;
    background:none;
    border:0;
    font-size:0;
    line-height:0;
    overflow:visible;
    cursor:pointer;
}

.btn label {
    cursor:pointer;
}

/* loading icon css */
//#container{display:none;}
#loading{
    position:fixed;
    display: block;
    //left: 50vw;
    //top: 50vw;
    margin-left:-30px;
    //top: calc(100% / 2);
    //left: calc(100% / 2);
    left: 40px;
    bottom: 10px;
    z-index: 1000;
}
@media screen and (max-width: 699px) {
    .photo-cell {
        position: relative;
        width: calc(100% / 2 - 10px);
        display: inline-block;
        //float: left;
        margin: 3px 5px;
        padding: 0 !important;
    }
    .photo-cell:before {
        content: "";
        display: block;
        padding-top: 100%;
    }
    .photo-cell img {
        position: absolute;
        width: 100%;
        height: 100% !important;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        object-fit: cover;
    }
    .photo-cell a img.trash-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: calc(100% - 30px);
    }
    .photo-cell a img.download-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: 5px;
    }
}
@media screen and (min-width: 700px) and (max-width: 999px) {
    .photo-cell {
        position: relative;
        width: calc(100% / 3 - 10px);
        display: inline-block;
        //float: left;
        margin: 3px 5px;
        padding: 0 !important;
    }
    .photo-cell:before {
        content: "";
        display: block;
        padding-top: 100%;
    }
    .photo-cell img {
        position: absolute;
        width: 100%;
        height: 100% !important;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        object-fit: cover;
    }
    .photo-cell a img.trash-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: calc(100% - 30px);
    }
    .photo-cell a img.download-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: 5px;
    }
}
@media screen and (min-width: 1000px) {
    .photo-cell {
        position: relative;
        width: calc(100% / 6 - 10px);
        display: inline-block;
        //float: left;
        margin: 3px 5px;
        padding: 0 !important;
    }
    .photo-cell:before {
        content: "";
        display: block;
        padding-top: 100%;
    }
    .photo-cell img {
        position: absolute;
        width: 100%;
        height: 100% !important;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        object-fit: cover;
    }
    .photo-cell a img.trash-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: calc(100% - 30px);
    }
    .photo-cell a img.download-icon {
        width: 25px;
        height: 25px !important;
        position: absolute;
        top: 7px;
        left: 5px;
    }
}

.row {
    display: flex;
    flex-wrap: wrap;
    padding: 0 4px;
}

/* Create four equal columns that sits next to each other */
.column {
    flex: 25%;
    max-width: 25%;
    padding: 0 4px;
}

.column img {
    margin-top: 8px;
    vertical-align: middle;
    width: 100%;
}

/* Responsive layout - makes a two column-layout instead of four columns */
@media screen and (max-width: 800px) {
    .column {
        flex: 50%;
        max-width: 50%;
    }
}

/* Responsive layout - makes the two columns stack on top of each other instead of next to each other */
@media screen and (max-width: 600px) {
    .column {
        flex: 100%;
        max-width: 100%;
    }
}

#login-card {
    margin: 20px 0;
}

.other-login-box {
    width: 400px;
    max-width: 70%;
    margin-bottom: 20px;
}

#title-nav {
    position: relative;
    display: block;
    height: $header-height;
    background-color: #3490dc;
    box-shadow: gainsboro  0 5px 2px ;
}

#app-title {
    cursor: pointer;
    color: whitesmoke;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    span {
        a:hover {
            text-decoration: none !important;
        }
        a {
            text-decoration: none !important;
        }
    }
}

#user-icon {
    float: right;
}
#user-icon img {
    width: 30px !important;
    border-radius: 3px;
}

#user-icon {
    background-color: unset;
    border-color: unset;
    border: none;
    position: absolute;
    right: 3px;
    top: 0
}

#user-icon:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

#event-detail-dialog-trigger img {
    width: 30px !important;
    border-radius: 3px;
}

#event-detail-dialog-trigger {
    background-color: unset;
    border-color: unset;
    border: none;
    position: absolute;
    left: 3px;
    top: 0
}

#event-detail-dialog-trigger:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}


main .container {
    overflow-x: hidden;
}

@media screen and (max-width: 699px) {
    //#photo-cards {
    //    /* Prevent vertical gaps */
    //    line-height: 0;
    //
    //    -webkit-column-count: 3;
    //    -webkit-column-gap: 0px;
    //    -moz-column-count: 3;
    //    -moz-column-gap: 0px;
    //    column-count: 3;
    //    column-gap: 0px;
    //
    //}
    //#photo-cards img {
    //    /* Just in case there are inline attributes */
    //    width: 100% !important;
    //    height: auto !important;
    //}
}

@media screen and (min-width: 700px) {
    //#photo-cards {
    //    /* Prevent vertical gaps */
    //    line-height: 0;
    //
    //    -webkit-column-count: 5;
    //    -webkit-column-gap: 0px;
    //    -moz-column-count: 5;
    //    -moz-column-gap: 0px;
    //    column-count: 5;
    //    column-gap: 0px;
    //
    //}
    //#photo-cards img {
    //    /* Just in case there are inline attributes */
    //    width: 100% !important;
    //    height: auto !important;
    //}
}

.card-img-top {
//    position: absolute;
//    width: 100%;
//    height: 100%;
//    //top: 0;
//    //right: 0;
//    //bottom: 0;
//    //left: 0;
//    //margin: auto;
//    object-fit: cover;
    border-radius: 3px;
}

#event-lib-slideshow {
    overflow: hidden;
}

.slick-list img {
    height: 80vh !important;
}

.iziToast-wrapper {
    position: absolute !important;
    top: 60px !important;
}
#uploading-toast .iziToast-close {
    display: none;
}
